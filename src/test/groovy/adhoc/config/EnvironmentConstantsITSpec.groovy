package adhoc.config

import adhoc.base.BaseAdhocITSpec
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@SpringBootTest(
classes = [BcmonitoringApplication.class],
webEnvironment = SpringBootTest.WebEnvironment.NONE
)
@ActiveProfiles("test")
class EnvironmentConstantsITSpec extends BaseAdhocITSpec {

	@Autowired
	Web3jConfig web3jConfig

	@Override
	Web3jConfig getWeb3jConfig() {
		return web3jConfig
	}

	@Autowired
	BcmonitoringConfigurationProperties properties

	def setupSpec() {
		setupSpecCommon()
	}

	def cleanupSpec() {
		cleanupSpecCommon()
	}

	def setup() {
		setupCommon()
	}

	def cleanup() {
		cleanupCommon()
	}

	/**
	 * Test that environment constants are properly initialized
	 * Verifies that TEST_BUCKET, EVENTS_TABLE, BLOCK_HEIGHT_TABLE are not null
	 * Expected: All constants should be initialized with environment-specific values
	 */
	def "Should initialize environment constants correctly"() {
		when: "Environment constants are checked"
		// Constants are initialized during @DynamicPropertySource execution

		then: "All constants should be initialized"
		TEST_BUCKET != null
		EVENTS_TABLE != null
		BLOCK_HEIGHT_TABLE != null

		and: "Constants should not be empty"
		!TEST_BUCKET.isEmpty()
		!EVENTS_TABLE.isEmpty()
		!BLOCK_HEIGHT_TABLE.isEmpty()

		and: "Constants should match test environment values"
		TEST_BUCKET == "abijson-local-bucket"
		EVENTS_TABLE == "local-Events"
		BLOCK_HEIGHT_TABLE == "local-BlockHeight"
	}

	/**
	 * Test that configuration properties match environment constants
	 * Verifies that Spring configuration uses the same values as test constants
	 * Expected: Configuration should be consistent with constants
	 */
	def "Should have configuration properties matching environment constants"() {
		when: "Configuration and constants are compared"
		// Both are loaded during application startup

		then: "Configuration should match constants"
		properties.getAws().getS3().getBucketName() == TEST_BUCKET
		properties.getAws().getDynamodb().getEventsTableName() == EVENTS_TABLE
		properties.getAws().getDynamodb().getBlockHeightTableName() == BLOCK_HEIGHT_TABLE

		and: "Environment should be consistent"
		properties.getEnv() == "local" // Test profile uses local environment
	}

	/**
	 * Test EnvironmentConfigManager utility methods
	 * Verifies that utility methods return correct values for test environment
	 * Expected: Utility methods should work correctly
	 */
	def "Should provide correct environment information via EnvironmentConfigManager"() {
		when: "Environment manager methods are called"
		def targetEnv = EnvironmentConfigManager.getTargetEnvironment()
		def constants = EnvironmentConfigManager.getEnvironmentConstants(targetEnv)
		def profile = EnvironmentConfigManager.getSpringProfile(targetEnv)

		then: "Environment manager should return correct values"
		targetEnv == "test"
		profile == "test"

		and: "Constants should match expected values"
		constants.testBucket == "abijson-local-bucket"
		constants.eventsTable == "local-Events"
		constants.blockHeightTable == "local-BlockHeight"

		and: "Constants should match actual initialized values"
		constants.testBucket == TEST_BUCKET
		constants.eventsTable == EVENTS_TABLE
		constants.blockHeightTable == BLOCK_HEIGHT_TABLE
	}

	/**
	 * Test that different environments would have different constants
	 * Verifies that EnvironmentConfigManager returns different values for different environments
	 * Expected: Each environment should have its own constants
	 */
	def "Should provide different constants for different environments"() {
		when: "Constants are retrieved for different environments"
		def localConstants = EnvironmentConfigManager.getEnvironmentConstants("local")
		def devConstants = EnvironmentConfigManager.getEnvironmentConstants("dev")
		def testConstants = EnvironmentConfigManager.getEnvironmentConstants("test")
		def prodConstants = EnvironmentConfigManager.getEnvironmentConstants("prod")

		then: "Local and test should have same values (both use LocalStack)"
		localConstants.testBucket == testConstants.testBucket
		localConstants.eventsTable == testConstants.eventsTable
		localConstants.blockHeightTable == testConstants.blockHeightTable

		and: "Dev should have different values"
		devConstants.testBucket == "abijson-dev-bucket"
		devConstants.eventsTable == "dev-Events"
		devConstants.blockHeightTable == "dev-BlockHeight"

		and: "Prod should have different values"
		prodConstants.testBucket == "abijson-prod-bucket"
		prodConstants.eventsTable == "prod-Events"
		prodConstants.blockHeightTable == "prod-BlockHeight"

		and: "All environments should have different bucket names (except local/test)"
		localConstants.testBucket != devConstants.testBucket
		devConstants.testBucket != prodConstants.testBucket
		localConstants.testBucket != prodConstants.testBucket
	}
}
