package adhoc.config

import org.springframework.test.context.DynamicPropertyRegistry

/**
 * Utility class for managing environment-specific configuration in tests.
 * This class provides methods to dynamically configure Spring properties based on the target environment.
 */
class EnvironmentConfigManager {

    /**
     * Environment constants
     */
    static final String ENV_LOCAL = "local"
    static final String ENV_DEV = "dev"
    static final String ENV_TEST = "test"
    static final String ENV_PROD = "prod"

    /**
     * Get the target environment from system property or environment variable.
     * Priority: System Property > Environment Variable > Default (test)
     * 
     * @return The target environment name
     */
    static String getTargetEnvironment() {
        return System.getProperty("test.environment") ?: 
               System.getenv("TEST_ENVIRONMENT") ?: 
               ENV_TEST
    }

    /**
     * Configure properties for the specified environment.
     * This method sets up environment-specific configuration overrides.
     * 
     * @param registry The DynamicPropertyRegistry to configure
     * @param environment The target environment (local, dev, test, prod)
     * @param localStackPort The LocalStack port (for local/test environments)
     */
    static void configureEnvironmentProperties(DynamicPropertyRegistry registry, String environment, String localStackPort = null) {
        switch (environment) {
            case ENV_LOCAL:
                configureLocalEnvironment(registry, localStackPort)
                break
            case ENV_DEV:
                configureDevEnvironment(registry)
                break
            case ENV_TEST:
                configureTestEnvironment(registry, localStackPort)
                break
            case ENV_PROD:
                configureProdEnvironment(registry)
                break
            default:
                throw new IllegalArgumentException("Unsupported environment: ${environment}")
        }
    }

    /**
     * Configure properties for local environment
     */
    private static void configureLocalEnvironment(DynamicPropertyRegistry registry, String localStackPort) {
        if (localStackPort) {
            registry.add("local-stack.end-point", { "http://localhost:${localStackPort}" })
            registry.add("aws.dynamodb.endpoint", { "http://localhost:${localStackPort}" })
        }
        registry.add("eagerStart", { "false" })
        registry.add("aws.dynamodb.table-prefix", { "local" })
        registry.add("env", { ENV_LOCAL })
    }

    /**
     * Configure properties for development environment
     */
    private static void configureDevEnvironment(DynamicPropertyRegistry registry) {
        registry.add("eagerStart", { "false" })
        registry.add("env", { ENV_DEV })
        
        // Override with dev-specific values if environment variables are set
        def devAwsRegion = System.getenv("DEV_AWS_REGION")
        if (devAwsRegion) {
            registry.add("aws.region", { devAwsRegion })
        }
        
        def devDynamoEndpoint = System.getenv("DEV_DYNAMODB_ENDPOINT")
        if (devDynamoEndpoint) {
            registry.add("aws.dynamodb.endpoint", { devDynamoEndpoint })
        }
        
        def devS3Bucket = System.getenv("DEV_S3_BUCKET_NAME")
        if (devS3Bucket) {
            registry.add("aws.s3.bucket-name", { devS3Bucket })
        }
    }

    /**
     * Configure properties for test environment
     */
    private static void configureTestEnvironment(DynamicPropertyRegistry registry, String localStackPort) {
        if (localStackPort) {
            registry.add("local-stack.end-point", { "http://localhost:${localStackPort}" })
            registry.add("aws.dynamodb.endpoint", { "http://localhost:${localStackPort}" })
        }
        registry.add("eagerStart", { "false" })
        registry.add("aws.dynamodb.table-prefix", { "" })
        registry.add("env", { ENV_LOCAL }) // Use local for test environment
        registry.add("subscription.check-interval", { "1000" }) // Faster for tests
    }

    /**
     * Configure properties for production environment
     */
    private static void configureProdEnvironment(DynamicPropertyRegistry registry) {
        registry.add("eagerStart", { "false" })
        registry.add("env", { ENV_PROD })
        
        // Override with prod-specific values if environment variables are set
        def prodAwsRegion = System.getenv("PROD_AWS_REGION")
        if (prodAwsRegion) {
            registry.add("aws.region", { prodAwsRegion })
        }
        
        def prodDynamoEndpoint = System.getenv("PROD_DYNAMODB_ENDPOINT")
        if (prodDynamoEndpoint) {
            registry.add("aws.dynamodb.endpoint", { prodDynamoEndpoint })
        }
        
        def prodS3Bucket = System.getenv("PROD_S3_BUCKET_NAME")
        if (prodS3Bucket) {
            registry.add("aws.s3.bucket-name", { prodS3Bucket })
        }
    }

    /**
     * Get the Spring profile name for the given environment
     * 
     * @param environment The target environment
     * @return The corresponding Spring profile name
     */
    static String getSpringProfile(String environment) {
        return environment
    }

    /**
     * Print current environment configuration for debugging
     */
    static void printEnvironmentInfo(String environment) {
        println("=== Environment Configuration ===")
        println("Target Environment: ${environment}")
        println("Spring Profile: ${getSpringProfile(environment)}")
        println("System Property 'test.environment': ${System.getProperty('test.environment')}")
        println("Environment Variable 'TEST_ENVIRONMENT': ${System.getenv('TEST_ENVIRONMENT')}")
        println("================================")
    }
}
