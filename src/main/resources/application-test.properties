# Test Environment Configuration
spring.application.name=Bcmonitoring

server.port=0

# AWS Configuration for Test Environment
aws.region=ap-northeast-1
aws.access-key-id=test-access-key
aws.secret-access-key=test-secret-key
aws.dynamodb.region=ap-northeast-1
aws.dynamodb.table-prefix=
aws.dynamodb.endpoint=http://localhost:4566
aws.s3.bucket-name=abijson-local-bucket
aws.s3.region=ap-northeast-1
aws.dynamodb.events-table-name=local-Events
aws.dynamodb.block-height-table-name=local-BlockHeight

# Ethereum Configuration for Test
ethereum.endpoint=

# WebSocket Configuration for Test
websocket.uri.host=localhost
websocket.uri.port=18541

# Subscription Configuration for Test
subscription.check-interval=1000
subscription.allowable-block-timestamp-diff-sec=2

# Environment Configuration
env=local
abi-format=hardhat
eager-start=false

# LocalStack Configuration for Test
local-stack.end-point=http://localhost:4566
local-stack.region=ap-northeast-1
local-stack.access-key=access123
local-stack.secret-key=secret123

# DynamoDB Table Configuration
dynamo-db.table.=bcmonitoring-test
